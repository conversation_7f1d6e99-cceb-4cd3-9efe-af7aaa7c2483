
from enum import Enum
import logging
import uuid
import hashlib
from pymongo import MongoClient
from datetime import datetime
import os

MONGO_URL = os.getenv('MONGO_URL', 'mongodb://root:<EMAIL>:27017')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
class RetroStatus(Enum):
    """
    Enum to represent the status of the retro synthesis process.
    """
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"

class DbOps:
    """
    A class to handle MongoDB operations for logging retro synthesis requests.
    """
    def __init__(self):
        self.client = MongoClient(MONGO_URL)
        self.db = self.client['retro_synthesis']
        self.requests_collection = self.db['retro_requests']
        # self._create_indexes()

    def _create_indexes(self):
        """
        Create necessary indexes for the collections.
        """
        self.requests_collection.create_index('request_id', unique=True)

    def insert_log(self, request_id, input_type, input_value, status, user_id='', tenant_id='', error_message=None):
        """
        Insert or update a log entry and track status history.
        """
        current_time = datetime.utcnow()
        
        # Prepare the document for new entries
        log_doc = {
            '_id' : str(uuid.uuid4()),
            'request_id': request_id,
            'input_type': input_type,
            'input_value': input_value,
            'status': status.value,
            'user_id': user_id,
            'tenant_id': tenant_id,
            'error_message': error_message,
            'created_at': current_time,
            'updated_at': current_time
        }

        # Update status and updated_at for existing documents, or insert new document
        self.requests_collection.update_one(
            {'request_id': request_id},
            {
            '$set': {
                'status': status.value,
                'updated_at': current_time
            },
            '$setOnInsert': {k: v for k, v in log_doc.items() if k not in ['status', 'updated_at']}
            },
            upsert=True
        )

        logging.info(f"Updated log for request_id: {request_id}, status: {status.value}")
    
    def insert_retro_data(self, request_id, unique_id, route_id, num_steps, total_route_score, target_smiles, total_cost ,raw_smiles, route_level_image, route_name, data):
        """
        Insert retro synthesis data into the database.
        """
        current_time = datetime.now()
        data_doc = {
            '_id' : str(uuid.uuid4()),
            'request_id': request_id,
            'route_id': route_id,
            'unique_id': unique_id,
            'target_smiles': target_smiles,
            'route_name': route_name,
            'route_reaction_img': route_level_image,
            'raw_reactants': raw_smiles,
            'num_steps': num_steps,
            'total_route_score': total_route_score,
            'total_cost': total_cost,
            'data': data,
            'created_at': current_time,
            'updated_at': current_time
        }
        try:
            # Update if unique_id exists, otherwise insert new document
            result = self.db['retro_data'].update_one(
                {'unique_id': unique_id},
                {
                    '$set': {
                        'updated_at': current_time
                    },
                    '$setOnInsert': {k: v for k, v in data_doc.items() if k != 'updated_at'}
                },
                upsert=True
            )
            if result.upserted_id:
                logging.info(f"Inserted new retro data for unique_id: {unique_id}")
            else:
                logging.info(f"Updated existing retro data for unique_id: {unique_id}")
        except Exception as e:
            logging.error(f"Failed to upsert retro data for unique_id {unique_id}: {str(e)}")

    def fetch_retro_data_by_target_smiles(self, target_smiles):
        """
        Fetch retro synthesis data by target SMILES.
        """
        try:
            return self.db['retro_data'].find({'target_smiles': target_smiles})
        except Exception as e:
            logging.error(f"Failed to fetch retro data for target_smiles {target_smiles}: {str(e)}")
            return []

    def close(self):
        """
        Close the MongoDB connection.
        
        """
        self.client.close()

