import pandas as pd

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from core.single_step_retro.disconnections import *
from infra.db.disconnections_fileStorage import *
from api.reaction_classification_api import *
from api.trasformers_api import *

import json

# Your JSON data
data = {
    "molecule_1": {
        "Target": "CS(=O)(=O)CC(F)F",
        "Precursor_1": "FC(F)CS(=O)(=O)Cl"
    },
    "molecule_2": {
        "Target": "[O-][N+](=O)c1cc(Cl)c(F)cc1Cl",
        "Precursor_1": "c1ccc(Cl)c(F)c1Cl",
        "Precursor_2": "[O-][N+](=O)c1cccc(Cl)c1F",
        "Precursor_3": "[O-][N+](=O)c1cccc(Cl)c1Cl"
    },
    "molecule_3": {
        "Target": "O=Cc1cc(C)ccc1Cl",
        "Precursor_1": "Cc1ccc(Cl)cc1Br",
        "Precursor_2": "Cc1ccc(N)cc1Br",
        "Precursor_3": "Cc1ccc([N+](=O)[O-])cc1Br",
        "Precursor_4": "Cc1ccc([N+](=O)[O-])cc1"
    },
    "molecule_4": {
        "Target": "NC(=O)c1ccncc1C(F)(F)F",
        "Precursor_1": "O=C(Cl)c1ccncc1C(F)(F)F",
        "Precursor_2": "O=C(O)c1ccncc1C(F)(F)F"
    },
    "molecule_5": {
        "Target": "Nc1ccc(OC(F)(F)F)cc1",
        "Precursor_1": "Nc1ccc(OC(F)(F)F)c(N)c1",
        "Precursor_2": "[O-][N+](=O)c1ccc(OC(F)(F)F)c([N+](=O)[O-])c1",
        "Precursor_3": "FC(F)(F)Oc1ccccc1",
        "Precursor_4": "ClC(Cl)(Cl)c1ccccc1",
        "Precursor_5": "COc1ccccc1"
    }
}

def get_consecutive_key_pairs(json_data):
    """
    Get consecutive pairs of keys from each inner dictionary
    """
    result = {}
    
    for molecule_key, molecule_data in json_data.items():
        keys = list(molecule_data.keys())
        consecutive_pairs = []
        
        # Get consecutive pairs of keys
        for i in range(len(keys) - 1):
            pair = (keys[i], keys[i + 1])
            consecutive_pairs.append(pair)
        
        result[molecule_key] = consecutive_pairs
    
    return result

def get_consecutive_key_pairs_with_values(json_data):
    """
    Get consecutive pairs of keys with their corresponding values
    """
    result = {}
    
    for molecule_key, molecule_data in json_data.items():
        keys = list(molecule_data.keys())
        consecutive_pairs = []
        
        # Get consecutive pairs of keys with values
        for i in range(len(keys) - 1):
            key1, key2 = keys[i], keys[i + 1]
            pair_data = {
                'keys': (key1, key2),
                'values': (molecule_data[key1], molecule_data[key2])
            }
            consecutive_pairs.append(pair_data)
        
        result[molecule_key] = consecutive_pairs
    
    return result

def canonicalize_smiles(smiles: str) -> str:
    '''
    Molecule canonicalization that does not change the SMILES order of molecules in case of multiple molecules.
    Also neutralizes any charge of the molecules.
    
    Args:
        smiles (str): SMILES string of the molecule(s).
    
    Returns:
        str: Canonicalized SMILES string of the molecule(s).
    '''
    returned = []
    any_error = False
    for molecule in smiles.split('.'):
        molecule = neutralize_smi(molecule)
        mol = Chem.MolFromSmiles(molecule)
        if mol is not None:
            returned.append(Chem.MolToSmiles(mol, isomericSmiles=True, canonical=True))
        else: 
            any_error = True
    if not any_error:
        return '.'.join(returned)
    else:
        return ''
    
def neutralize_smi(smiles: str) -> str:        # from: https://www.rdkit.org/docs/Cookbook.html#neutralizing-molecules
    if '-' in smiles or '+' in smiles:
        try:
            mol = Chem.MolFromSmiles(smiles)
            pattern = Chem.MolFromSmarts("[+1!h0!$([*]~[-1,-2,-3,-4]),-1!$([*]~[+1,+2,+3,+4])]")
            at_matches = mol.GetSubstructMatches(pattern)
            at_matches_list = [y[0] for y in at_matches]
            if len(at_matches_list) > 0:
                for at_idx in at_matches_list:
                    atom = mol.GetAtomWithIdx(at_idx)
                    chg = atom.GetFormalCharge()
                    hcount = atom.GetTotalNumHs()
                    atom.SetFormalCharge(0)
                    atom.SetNumExplicitHs(hcount - chg)
                    atom.UpdatePropertyCache()
            return Chem.MolToSmiles(mol)
        except:
            return smiles
    else:
        return smiles

def check_reactant_in_retro(df, reactant, column_name='Retro'):
    """
    Check if a given reactant is present in the Retro column of a dataframe.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        The dataframe containing the Retro column
    reactant : str
        The reactant SMILES string to search for
    column_name : str, default 'Retro'
        The name of the column containing reactants separated by '.'
    
    Returns:
    --------
    pandas.Series
        Boolean series indicating whether the reactant is present in each row
    """
    return df[column_name].str.contains(f'\\b{reactant}\\b', regex=True, na=False)

def check_reactant_exact_match(df, reactant, column_name='Retro'):
    """
    Check if a given reactant is present in the Retro column using exact matching.
    This splits on '.' and checks for exact matches in the list of reactants.
    
    Parameters:
    -----------
    df : pandas.DataFrame
        The dataframe containing the Retro column
    reactant : str
        The reactant SMILES string to search for
    column_name : str, default 'Retro'
        The name of the column containing reactants separated by '.'
    
    Returns:
    --------
    pandas.Series
        Boolean series indicating whether the reactant is present in each row
    """
    return df[column_name].apply(
        lambda x: reactant in x.split('.') if pd.notna(x) else False
    )

#Example usage:
#Check if 'Cc1cc(C(=O)O)cc(C(=O)O)c1N' is present
# result = check_reactant_exact_match(df, 'Cc1cc(C(=O)O)cc(C(=O)O)c1N')

# Or check multiple reactants:
# reactants_to_check = ['Cc1cc(C(=O)O)cc(C(=O)O)c1N', 'Cc1cc(CO)cc(C(=O)O)c1N']
# for reactant in reactants_to_check:
#     mask = check_reactant_exact_match(df, reactant)
#     print(f"Reactant '{reactant}' found in {mask.sum()} rows")


if __name__=="__main__":
    with open("input/precursors_simple.json", "r") as f:
        data = json.load(f)

    # Method 2: Get consecutive key pairs with their values
    results_df = pd.DataFrame()
    results_df_details = pd.DataFrame()
    rows_details = []
    
    print("Method 2: Consecutive key pairs with values")
    pairs_with_values = get_consecutive_key_pairs_with_values(data)
    for molecule, pairs in pairs_with_values.items():
        print(f"{molecule}:")
        for i, pair_info in enumerate(pairs, 1):
            keys = pair_info['keys']
            values = pair_info['values']
            print(f"  Pair {i}: {keys[0]} -> {keys[1]}")
            print(f"    {keys[0]}: {values[0]}")
            print(f"    {keys[1]}: {values[1]}")
           
            target_smile = values[0]
            precusor_smile = values[1]
            print(f"{molecule},{target_smile},{precusor_smile}")
            #exit(1)
            #storage = DisconnectionsStorage('opt/disconnections') 
            storage = DisconnectionsStorage('opt/disconnections_relaxed') 
            target_predictions = storage.load_disconnections(canonicalize_smiles(target_smile))         
            print(f"Loaded {len(target_predictions)} records from fileStorage")  
            if len(target_predictions) >0:
                print(target_predictions.shape)
                #print(target_predictions.iloc[0])
                relaxed = 1
                if relaxed == 0:    
                    target_predictions= target_predictions[['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']]
                else:
                    target_predictions= target_predictions[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf']]

                #target_predictions.columns=['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']#, 'rxn_string', 'rxn_class']
                result = check_reactant_exact_match(target_predictions, canonicalize_smiles(precusor_smile))
                print(type(result))
                print(f"Loaded {len(target_predictions[result])} match records from fileStorage")
                
                row = {
                    'molecule': molecule,
                    'target': target_smile,
                    'precursor': precusor_smile,
                    '#match': len(target_predictions[result])
                }
                results_df = results_df.append(row, ignore_index=True)

                matched_df = target_predictions[result]  # result must be a boolean mask here

                # Log how many matched
                print(f"Loaded {len(matched_df)} match records from fileStorage")

                # Iterate over the matched rows
                for idx, row in matched_df.iterrows():
                    rows_details.append([
                        molecule,
                        target_smile,
                        precusor_smile,
                        row['T1_Model'],
                        row['ID'],
                        row['ID_Tag'],
                        row['ID_beam'],
                        row['Target'],
                        row['Tagged_Target'],
                        row['Retro'],
                        row['Retro_Conf']
                    ])  

                #print(len(target_predictions[result]))
                #print(target_predictions[result].iloc[0])
            
                # if len(target_predictions[result]) > 0:
                #     #post filtering misses  
                #     #remove invalid discconections by roundtrip logid
                #     # Keep predictions where T3 predicts the correct target, and target is not in the retro prediction:
            
                #     print(target_predictions.shape)
                #     target_predictions_Forw_val = target_predictions[target_predictions['Target'] == target_predictions['Forward_Prediction']]
                #     print(target_predictions_Forw_val.shape)
                #     target_predictions_Forw_val = target_predictions_Forw_val[target_predictions_Forw_val['Target'] != target_predictions_Forw_val['Retro']]
                #     print(target_predictions_Forw_val.shape)        

                #     #target_predictions_Forw_val['rxn_string'] =target_predictions_Forw_val.apply(lambda s: s['Retro'] + '>' + s['Reagents'] + '>' + target_smile,axis=1)
                #     #target_predictions_Forw_val['rxn_class'] = target_predictions_Forw_val.apply(lambda s: get_reaction_class(s['rxn_string']),axis=1)
                #     print(target_predictions_Forw_val.shape) 
                #     result_filtered = check_reactant_exact_match(target_predictions_Forw_val, canonicalize_smiles(precusor_1_smile))
                #     print(f"Loaded {len(target_predictions_Forw_val[result_filtered])} match records from fileStorage")
    results_df_details = pd.DataFrame(rows_details, columns=[
        'molecule', 'target', 'precursor', 'T1_Model',
        'ID', 'ID_Tag', 'ID_beam', 'Target',
        'Tagged_Target', 'Retro', 'Retro_Conf'
        ])

    results_df.to_csv("output/match_results_df.csv")
    results_df_details.to_csv("output/match_results_details_df.csv")