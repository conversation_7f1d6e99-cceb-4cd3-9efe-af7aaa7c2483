import pandas as pd

import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from core.single_step_retro.disconnections import *
from infra.db.disconnections_fileStorage import *
from api.reaction_classification_api import *
from api.trasformers_api import *
from api.synthesis_score_api import *
from utils.detect_reactant_json import *

import json
import pandas as pd
import numpy as np
from typing import List, Optional
from scscore import SCScorer
from abc import ABC, abstractmethod

# Your existing classes (included for completeness)
class SynthesisScoreAPI(ABC):
    """Abstract API for getting synthesis scores."""
    
    @abstractmethod
    def get_synthesis_score(self, smiles: str) -> float:
        """Get synthesis score for a molecule."""
        pass

class SCScoreAPI(SynthesisScoreAPI):
    """SCScore implementation."""
    
    def __init__(self):
        self.scs_scorer = SCScorer()
    
    def get_synthesis_score(self, smiles: str) -> float:
        return self.scs_scorer.get_score_from_smi(smiles)[1]

class TerminalChecker(ABC):
    """Abstract base class for terminal condition checkers."""
    
    @abstractmethod
    def is_terminal_smiles(self, smiles: str, synthesis_score: float) -> bool:
        """Check if a SMILES should be considered terminal."""
        pass

class SynthesisScoreTerminalChecker(TerminalChecker):
    """Check if molecule is terminal based on synthesis score."""
    
    def __init__(self, threshold: float):
        self.threshold = threshold
    
    def is_terminal_smiles(self, smiles: str, synthesis_score: float) -> bool:
        return synthesis_score < self.threshold

def parse_reactants(retro_smiles: str) -> List[str]:
    """
    Parse reactants from the Retro field.
    Assumes reactants are separated by '.' (common in SMILES notation).
    You may need to adjust the delimiter based on your data format.
    """
    if pd.isna(retro_smiles) or retro_smiles == '':
        return []
    
    # Split by '.' which is common for separating molecules in SMILES
    # You might need to adjust this delimiter based on your data
    reactants = [smiles.strip() for smiles in retro_smiles.split('.') if smiles.strip()]
    return reactants

def enhance_dataframe_with_synthesis_scores(df: pd.DataFrame, 
                                          synthesis_score_api: SynthesisScoreAPI,
                                          terminal_threshold: float = 1.5) -> pd.DataFrame:
    """
    Add synthesis score columns to the dataframe.
    
    Args:
        df: Input dataframe
        synthesis_score_api: API for getting synthesis scores
        terminal_threshold: Threshold for terminal condition
    
    Returns:
        Enhanced dataframe with additional columns
    """
    # Create a copy to avoid modifying the original
    enhanced_df = df.copy()
    
    # Initialize new columns
    enhanced_df['num_reactants'] = 0
    enhanced_df['total_synthesis_score'] = np.nan
    enhanced_df['max_synthesis_score'] = np.nan
    enhanced_df['min_synthesis_score'] = np.nan
    enhanced_df['has_terminal_reactant'] = False
    
    # Create terminal checker
    terminal_checker = SynthesisScoreTerminalChecker(terminal_threshold)
    
    for idx, row in enhanced_df.iterrows():
        try:
            # Parse reactants from Retro field
            reactants = parse_reactants(row['Retro'])
            num_reactants = len(reactants)
            enhanced_df.at[idx, 'num_reactants'] = num_reactants
            
            if num_reactants == 0:
                continue
            
            # Get synthesis scores for all reactants
            synthesis_scores = []
            has_terminal = False
            
            for reactant_smiles in reactants:
                try:
                    score = synthesis_score_api.get_synthesis_score(reactant_smiles)
                    synthesis_scores.append(score)
                    
                    # Check if this reactant is terminal
                    if terminal_checker.is_terminal_smiles(reactant_smiles, score):
                        has_terminal = True
                        
                except Exception as e:
                    print(f"Error getting synthesis score for {reactant_smiles}: {e}")
                    # You might want to handle this differently based on your needs
                    continue
            
            # Calculate aggregate scores
            if synthesis_scores:
                enhanced_df.at[idx, 'total_synthesis_score'] = sum(synthesis_scores)
                enhanced_df.at[idx, 'max_synthesis_score'] = max(synthesis_scores)
                enhanced_df.at[idx, 'min_synthesis_score'] = min(synthesis_scores)
                enhanced_df.at[idx, 'has_terminal_reactant'] = has_terminal
            
        except Exception as e:
            print(f"Error processing row {idx}: {e}")
            continue
    
    return enhanced_df

# Example usage
def main():
    # Initialize the synthesis score API
    synthesis_score_api = SCScoreAPI()
    
    # Assuming you have your dataframe loaded as 'df'
    # df = pd.read_csv('your_data.csv')  # or however you load your data
    
    # Enhance the dataframe
    enhanced_df = enhance_dataframe_with_synthesis_scores(
        df=df,  # your original dataframe
        synthesis_score_api=synthesis_score_api,
        terminal_threshold=1.5
    )
    
    # Display the new columns
    new_columns = ['num_reactants', 'total_synthesis_score', 'max_synthesis_score', 
                   'min_synthesis_score', 'has_terminal_reactant']
    print("New columns added:")
    print(enhanced_df[new_columns].head())
    
    return enhanced_df

# Alternative function if you want to add columns one by one
def add_synthesis_score_columns(df: pd.DataFrame, 
                               synthesis_score_api: SynthesisScoreAPI,
                               terminal_threshold: float = 1.5):
    """
    Add synthesis score columns to existing dataframe (modifies in place).
    """
    # Function to process each row
    def process_row(row):
        reactants = parse_reactants(row['Retro'])
        num_reactants = len(reactants)
        
        if num_reactants == 0:
            return pd.Series({
                'num_reactants': 0,
                'total_synthesis_score': np.nan,
                'max_synthesis_score': np.nan,
                'min_synthesis_score': np.nan,
                'has_terminal_reactant': False
            })
        
        synthesis_scores = []
        has_terminal = False
        terminal_checker = SynthesisScoreTerminalChecker(terminal_threshold)
        
        for reactant_smiles in reactants:
            try:
                score = synthesis_score_api.get_synthesis_score(reactant_smiles)
                synthesis_scores.append(score)
                
                if terminal_checker.is_terminal_smiles(reactant_smiles, score):
                    has_terminal = True
                    
            except Exception as e:
                print(f"Error getting synthesis score for {reactant_smiles}: {e}")
                continue
        
        if synthesis_scores:
            return pd.Series({
                'num_reactants': num_reactants,
                'total_synthesis_score': sum(synthesis_scores),
                'max_synthesis_score': max(synthesis_scores),
                'min_synthesis_score': min(synthesis_scores),
                'has_terminal_reactant': has_terminal
            })
        else:
            return pd.Series({
                'num_reactants': num_reactants,
                'total_synthesis_score': np.nan,
                'max_synthesis_score': np.nan,
                'min_synthesis_score': np.nan,
                'has_terminal_reactant': False
            })
    
    # Apply the function to create new columns
    new_columns = df.apply(process_row, axis=1)
    
    # Add the new columns to the original dataframe
    for col in new_columns.columns:
        df[col] = new_columns[col]
    
    return df

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Callable
import pandas as pd

class ScoringComponent(ABC):
    """Abstract base class for scoring components."""
    
    @abstractmethod
    def calculate_score(self, row: pd.Series) -> float:
        """Calculate score for a single row."""
        pass
    
    @abstractmethod
    def get_weight(self) -> float:
        """Get the weight for this component."""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the name of this scoring component."""
        pass

class SynthesisScoreComponent(ScoringComponent):
    """Scoring component based on synthesis scores."""
    
    def __init__(self, weight: float = 1.0, score_type: str = 'min'):
        """
        Args:
            weight: Weight for this component in final score
            score_type: 'min', 'max', 'total', or 'avg'
        """
        self.weight = weight
        self.score_type = score_type
        self.name = f"synthesis_score_{score_type}"
    
    def calculate_score(self, row: pd.Series) -> float:
        if self.score_type == 'min':
            score = row.get('min_synthesis_score', np.nan)
        elif self.score_type == 'max':
            score = row.get('max_synthesis_score', np.nan)
        elif self.score_type == 'total':
            score = row.get('total_synthesis_score', np.nan)
        elif self.score_type == 'avg':
            total = row.get('total_synthesis_score', np.nan)
            num_reactants = row.get('num_reactants', 0)
            if num_reactants > 0 and not pd.isna(total):
                score = total / num_reactants
            else:
                score = np.nan
        else:
            raise ValueError(f"Unknown score_type: {self.score_type}")
        
        # Handle NaN values - return 0 for missing synthesis scores
        if pd.isna(score):
            return 0.0
        
        # Handle edge cases
        if not np.isfinite(score):
            return 0.0
            
        # Normalize score (assuming SCScore ranges from 1-5, higher is harder to synthesize)
        # We invert it so higher final score = better
        # Clamp score to reasonable range first
        score = max(1.0, min(5.0, score))
        normalized_score = (5.0 - score) / 4.0  # Convert to 0-1 range, inverted
        
        return max(0.0, min(1.0, normalized_score))
    
    def get_weight(self) -> float:
        return self.weight
    
    def get_name(self) -> str:
        return self.name

class ReactantCountComponent(ScoringComponent):
    """Scoring component based on number of reactants."""
    
    def __init__(self, weight: float = 0.5, prefer_fewer: bool = True):
        """
        Args:
            weight: Weight for this component
            prefer_fewer: If True, fewer reactants get higher score
        """
        self.weight = weight
        self.prefer_fewer = prefer_fewer
        self.name = "reactant_count"
    
    def calculate_score(self, row: pd.Series) -> float:
        num_reactants = row.get('num_reactants', 0)
        
        if num_reactants == 0:
            return 0.0
        
        # Normalize based on typical range (1-5 reactants)
        if self.prefer_fewer:
            # Fewer reactants = higher score
            score = max(0, (5 - num_reactants) / 4)
        else:
            # More reactants = higher score
            score = min(1, num_reactants / 5)
        
        return max(0, min(1, score))
    
    def get_weight(self) -> float:
        return self.weight
    
    def get_name(self) -> str:
        return self.name

class TerminalPenaltyComponent(ScoringComponent):
    """Scoring component that penalizes terminal reactants."""
    
    def __init__(self, weight: float = 1.0, penalty: float = 0.5):
        """
        Args:
            weight: Weight for this component
            penalty: Penalty factor (0-1) for having terminal reactants
        """
        self.weight = weight
        self.penalty = penalty
        self.name = "terminal_penalty"
    
    def calculate_score(self, row: pd.Series) -> float:
        has_terminal = row.get('has_terminal_reactant', False)
        
        if has_terminal:
            return 1.0 - self.penalty  # Apply penalty
        else:
            return 1.0  # No penalty
    
    def get_weight(self) -> float:
        return self.weight
    
    def get_name(self) -> str:
        return self.name

class ConfidenceComponent(ScoringComponent):
    """Scoring component based on retrosynthesis confidence."""
    
    def __init__(self, weight: float = 0.8):
        self.weight = weight
        self.name = "retro_confidence"
    
    def calculate_score(self, row: pd.Series) -> float:
        confidence = row.get('Retro_Conf', np.nan)
        
        if pd.isna(confidence):
            return 0.0
        
        # Handle infinite or invalid values
        if not np.isfinite(confidence):
            return 0.0
        
        # Confidence should be normalized (0-1), but let's be safe
        return max(0.0, min(1.0, float(confidence)))
    
    def get_weight(self) -> float:
        return self.weight
    
    def get_name(self) -> str:
        return self.name

class CustomFunctionComponent(ScoringComponent):
    """Scoring component that uses a custom function."""
    
    def __init__(self, func: Callable[[pd.Series], float], weight: float, name: str):
        """
        Args:
            func: Function that takes a pandas Series (row) and returns a float
            weight: Weight for this component
            name: Name for this component
        """
        self.func = func
        self.weight = weight
        self.name = name
    
    def calculate_score(self, row: pd.Series) -> float:
        try:
            score = self.func(row)
            return max(0, min(1, score))  # Ensure 0-1 range
        except Exception as e:
            print(f"Error in custom function {self.name}: {e}")
            return 0.0
    
    def get_weight(self) -> float:
        return self.weight
    
    def get_name(self) -> str:
        return self.name

class CompositeScoringSystem:
    """Composite scoring system that combines multiple scoring components."""
    
    def __init__(self, components: List[ScoringComponent]):
        self.components = components
    
    def add_component(self, component: ScoringComponent):
        """Add a new scoring component."""
        self.components.append(component)
    
    def remove_component(self, name: str):
        """Remove a scoring component by name."""
        self.components = [c for c in self.components if c.get_name() != name]
    
    def calculate_final_score(self, row: pd.Series) -> Dict[str, float]:
        """Calculate final score and component scores for a row."""
        component_scores = {}
        weighted_sum = 0
        total_weight = 0
        
        for component in self.components:
            try:
                score = component.calculate_score(row)
                weight = component.get_weight()
                
                # Ensure score is finite and in valid range
                if not np.isfinite(score):
                    score = 0.0
                score = max(0.0, min(1.0, score))  # Clamp to [0, 1]
                
                component_scores[f"{component.get_name()}_score"] = score
                component_scores[f"{component.get_name()}_weighted"] = score * weight
                
                weighted_sum += score * weight
                total_weight += weight
                
            except Exception as e:
                print(f"Error calculating score for component {component.get_name()}: {e}")
                # Use default values for problematic components
                component_scores[f"{component.get_name()}_score"] = 0.0
                component_scores[f"{component.get_name()}_weighted"] = 0.0
                total_weight += component.get_weight()  # Still count the weight
        
        # Calculate final score as weighted average
        if total_weight > 0:
            final_score = weighted_sum / total_weight
        else:
            final_score = 0.0
            
        # Ensure final score is finite and in valid range
        if not np.isfinite(final_score):
            final_score = 0.0
        final_score = max(0.0, min(1.0, final_score))
        
        component_scores['final_score'] = final_score
        
        return component_scores
    
    def get_component_names(self) -> List[str]:
        """Get names of all components."""
        return [c.get_name() for c in self.components]

def create_default_scoring_system() -> CompositeScoringSystem:
    """Create a default scoring system with common components."""
    components = [
        SynthesisScoreComponent(weight=2.0, score_type='min'),  # Prioritize min score
        ReactantCountComponent(weight=1.0, prefer_fewer=True),   # Prefer fewer reactants
        TerminalPenaltyComponent(weight=1.5, penalty=0.3)     # Penalize terminal reactants
        #ConfidenceComponent(weight=1.0)                         # Include retro confidence
    ]
    
    return CompositeScoringSystem(components)

def add_scoring_and_ranking(df: pd.DataFrame, 
                           scoring_system: CompositeScoringSystem = None,
                           rank_ascending: bool = False) -> pd.DataFrame:
    """
    Add final scores and ranking to the dataframe.
    
    Args:
        df: Input dataframe
        scoring_system: Custom scoring system (uses default if None)
        rank_ascending: If True, rank 1 = lowest score, if False, rank 1 = highest score
    
    Returns:
        Dataframe with added scoring and ranking columns
    """
    if scoring_system is None:
        scoring_system = create_default_scoring_system()
    
    # Calculate scores for each row
    scoring_results = []
    for idx, row in df.iterrows():
        try:
            scores = scoring_system.calculate_final_score(row)
            scoring_results.append(scores)
        except Exception as e:
            print(f"Error calculating scores for row {idx}: {e}")
            # Create default scores for problematic rows
            default_scores = {f"{comp.get_name()}_score": 0.0 for comp in scoring_system.components}
            default_scores.update({f"{comp.get_name()}_weighted": 0.0 for comp in scoring_system.components})
            default_scores['final_score'] = 0.0
            scoring_results.append(default_scores)
    
    # Convert to dataframe and merge
    scoring_df = pd.DataFrame(scoring_results)
    result_df = pd.concat([df, scoring_df], axis=1)
    
    # Handle NaN and infinite values in final_score
    final_scores = result_df['final_score'].copy()
    
    # Replace NaN values with 0
    final_scores = final_scores.fillna(0.0)
    
    # Replace infinite values with appropriate bounds
    final_scores = final_scores.replace([np.inf, -np.inf], [1.0, 0.0])
    
    # Ensure all values are finite
    final_scores = np.where(np.isfinite(final_scores), final_scores, 0.0)
    
    # Update the dataframe with cleaned scores
    result_df['final_score'] = final_scores
    
    # Add ranking based on final score (now safe to convert to int)
    try:
        result_df['rank'] = result_df['final_score'].rank(
            ascending=rank_ascending, 
            method='dense',
            na_option='bottom'  # Put any remaining NaN at the bottom
        ).astype(int)
    except Exception as e:
        print(f"Error creating ranks: {e}")
        # Fallback: create sequential ranks
        result_df = result_df.sort_values('final_score', ascending=rank_ascending)
        result_df['rank'] = range(1, len(result_df) + 1)
    
    return result_df

# Example usage and custom scoring functions
def example_custom_scoring_functions():
    """Examples of custom scoring functions."""
    
    def molecular_weight_score(row):
        """Example: Score based on molecular weight of target."""
        # You'd need to calculate MW from SMILES
        # This is just a placeholder
        return 0.5
    
    def complexity_score(row):
        """Example: Score based on molecular complexity."""
        target_smiles = row.get('Target', '')
        # Simple complexity metric based on SMILES length
        if target_smiles:
            normalized_length = min(len(target_smiles) / 100, 1.0)
            return 1.0 - normalized_length  # Simpler molecules get higher scores
        return 0.0
    
    return [
        CustomFunctionComponent(molecular_weight_score, 0.5, "molecular_weight"),
        CustomFunctionComponent(complexity_score, 0.3, "complexity")
    ]

# Enhanced main function
def main_with_scoring():
    """Enhanced main function with scoring and ranking."""
    
    # Initialize the synthesis score API
    synthesis_score_api = SCScoreAPI()
    
    # Enhance the dataframe with synthesis scores
    enhanced_df = enhance_dataframe_with_synthesis_scores(
        df=df,  # your original dataframe
        synthesis_score_api=synthesis_score_api,
        terminal_threshold=1.5
    )
    
    # Create custom scoring system
    scoring_system = create_default_scoring_system()
    
    # Add custom components (optional)
    custom_components = example_custom_scoring_functions()
    for component in custom_components:
        scoring_system.add_component(component)
    
    # Add scoring and ranking
    final_df = add_scoring_and_ranking(
        df=enhanced_df,
        scoring_system=scoring_system,
        rank_ascending=False  # Higher scores get better (lower) ranks
    )
    
    # Display results
    score_columns = ['final_score', 'rank'] + [f"{name}_score" for name in scoring_system.get_component_names()]
    print("Scoring results:")
    print(final_df[score_columns].head(10))
    
    # Show top-ranked entries
    print("\nTop 5 ranked entries:")
    top_entries = final_df.nsmallest(5, 'rank')[['ID', 'Target', 'final_score', 'rank']]
    print(top_entries)
    
    return final_df

if __name__=="__main__":

    with open("input/precursors_simple.json", "r") as f:
        data = json.load(f)

    synthesis_score_api=SCScoreAPI()
    scoring_system = create_default_scoring_system()

    # Method 2: Get consecutive key pairs with their values
    results_df = pd.DataFrame()
    results_df_details = pd.DataFrame()
    rows_details = []
    
    print("Method 2: Consecutive key pairs with values")
    pairs_with_values = get_consecutive_key_pairs_with_values(data)
    for molecule, pairs in pairs_with_values.items():
        print(f"{molecule}:")
        for i, pair_info in enumerate(pairs, 1):
            keys = pair_info['keys']
            values = pair_info['values']
            print(f"  Pair {i}: {keys[0]} -> {keys[1]}")
            print(f"    {keys[0]}: {values[0]}")
            print(f"    {keys[1]}: {values[1]}")
           
            target_smile = values[0]
            precusor_smile = values[1]
            print(f"{molecule},{target_smile},{precusor_smile}")
            #exit(1)
            #storage = DisconnectionsStorage('opt/disconnections') 
            storage = DisconnectionsStorage('opt/disconnections_relaxed') 
            target_predictions = storage.load_disconnections(canonicalize_smiles(target_smile))         
            print(f"Loaded {len(target_predictions)} records from fileStorage")  
            #print(target_predictions.iloc[0])
            print(target_predictions.shape)
            target_predictions=target_predictions.drop_duplicates(subset=['Retro'], keep='first').copy()
            print(target_predictions.shape)

            #target_predictions['rxn_string'] =target_predictions.apply(lambda s: s['Retro'] + '>' + target_smile,axis=1)
            #target_predictions['rxn_class'] = target_predictions.apply(lambda s: get_reaction_class(s['rxn_string']),axis=1)   

            target_predictions = enhance_dataframe_with_synthesis_scores(target_predictions, synthesis_score_api)
            print(target_predictions.shape)
            print(target_predictions.iloc[0])
            #exit(1)
            target_predictions = add_scoring_and_ranking(target_predictions, scoring_system)
            print(target_predictions.shape)

            target_predictions.to_parquet(f"final_{molecule}_{target_smile}.parquet")
            if len(target_predictions) >0:
                print(target_predictions.shape)
                #print(target_predictions.iloc[0])
                relaxed = 1
                if relaxed == 0:    
                    target_predictions= target_predictions[['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']]
                else:
                    target_predictions= target_predictions[['T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','num_reactants','total_synthesis_score','max_synthesis_score','min_synthesis_score','has_terminal_reactant','final_score','rank']]

                #target_predictions.columns=['index', 'T1_Model', 'ID', 'ID_Tag', 'ID_beam', 'Target', 'Tagged_Target', 'Retro', 'Retro_Conf','Forward_Model', 'Reagents', 'Forward_Prediction', 'Prob_Forward_Prediction_1','Prob_Forward_Prediction_2']#, 'rxn_string', 'rxn_class']
                result = check_reactant_exact_match(target_predictions, canonicalize_smiles(precusor_smile))
                target_predictions["match"] = result
                print(type(result))
                print(f"Loaded {len(target_predictions[result])} match records from fileStorage")
                
                row = {
                    'molecule': molecule,
                    'target': target_smile,
                    'precursor': precusor_smile,
                    '#match': len(target_predictions[result])
                }
                results_df = results_df.append(row, ignore_index=True)

                matched_df = target_predictions#[result]  # result must be a boolean mask here

                # Log how many matched
                print(f"Loaded {len(matched_df)} match records from fileStorage")

                # Iterate over the matched rows
                for idx, row in matched_df.iterrows():
                    rows_details.append([
                        molecule,
                        target_smile,
                        precusor_smile,
                        row['T1_Model'],
                        row['ID'],
                        row['ID_Tag'],
                        row['ID_beam'],
                        row['Target'],
                        row['Tagged_Target'],
                        row['Retro'],
                        row['Retro_Conf'],
                        row['num_reactants'],
                        row['total_synthesis_score'],
                        row['max_synthesis_score'],
                        row['min_synthesis_score'],
                        row['has_terminal_reactant'],
                        row['match'],                        
                        row['final_score'],
                        row['rank']
                    ])  
                # results_df_details = pd.DataFrame(rows_details, columns=[
                #     'molecule', 'target', 'precursor', 'T1_Model',
                #     'ID', 'ID_Tag', 'ID_beam', 'Target',
                #     'Tagged_Target', 'Retro', 'Retro_Conf','num_reactants','total_synthesis_score','max_synthesis_score','min_synthesis_score','has_terminal_reactant','match','final_score','rank'])             
                # results_df_details.to_csv("output/match_results_details_score_df.csv")
                #print(len(target_predictions[result]))
                #print(target_predictions[result].iloc[0])
            
                # if len(target_predictions[result]) > 0:
                #     #post filtering misses  
                #     #remove invalid discconections by roundtrip logid
                #     # Keep predictions where T3 predicts the correct target, and target is not in the retro prediction:
            
                #     print(target_predictions.shape)
                #     target_predictions_Forw_val = target_predictions[target_predictions['Target'] == target_predictions['Forward_Prediction']]
                #     print(target_predictions_Forw_val.shape)
                #     target_predictions_Forw_val = target_predictions_Forw_val[target_predictions_Forw_val['Target'] != target_predictions_Forw_val['Retro']]
                #     print(target_predictions_Forw_val.shape)        

                #     #target_predictions_Forw_val['rxn_string'] =target_predictions_Forw_val.apply(lambda s: s['Retro'] + '>' + s['Reagents'] + '>' + target_smile,axis=1)
                #     #target_predictions_Forw_val['rxn_class'] = target_predictions_Forw_val.apply(lambda s: get_reaction_class(s['rxn_string']),axis=1)
                #     print(target_predictions_Forw_val.shape) 
                #     result_filtered = check_reactant_exact_match(target_predictions_Forw_val, canonicalize_smiles(precusor_1_smile))
                #     print(f"Loaded {len(target_predictions_Forw_val[result_filtered])} match records from fileStorage")
    results_df_details = pd.DataFrame(rows_details, columns=[
        'molecule', 'target', 'precursor', 'T1_Model',
        'ID', 'ID_Tag', 'ID_beam', 'Target',
        'Tagged_Target', 'Retro', 'Retro_Conf','num_reactants','total_synthesis_score','max_synthesis_score','min_synthesis_score','has_terminal_reactant','match','final_score','rank'])             
    results_df_details.to_csv("output/match_results_details_score_df.csv")

    results_df.to_csv("output/match_results_score_df.csv")
    results_df_details.to_csv("output/match_results_details_score_df.csv")