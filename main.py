import os
import json
import logging
import traceback
import time
import socket
import redis
from http import HTT<PERSON>tatus
from http.server import HTTPServer, BaseHTTPRequestHandler
from http.server import ThreadingHTTPServer
from utils.helpers import REQUEST_ID



from celery import Celery
from celery.signals import after_setup_logger
from retro_runner import retro_runner
from threading import Thread
from db.db_ops import DbOps, RetroStatus


# Logging configuration
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger(__name__)

# Celery configuration
REDIS_URL = os.getenv("REDIS_URL", "redis://host.docker.internal:6379/0")
INPUT_REDIS_QUEUE = os.getenv("RETRO_INPUT_REDIS_QUEUE", "retro_input_job_queue")
OUTPUT_QUEUE = os.getenv("RETRO_OUTPUT_REDIS_QUEUE", "target_retro_job_queue")

app = Celery(__name__, broker=REDIS_URL, backend=REDIS_URL)
app.conf.update(
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_disable_rate_limits=True,
    task_reject_on_worker_lost=True,
    task_ignore_result=False,
    worker_max_tasks_per_child=1,
    task_default_queue=INPUT_REDIS_QUEUE,  # Add this line
    task_routes={
        'process_retro_task': {'queue': INPUT_REDIS_QUEUE}
    }
)

@after_setup_logger.connect
def setup_celery_logger(logger, *args, **kwargs):
    os.makedirs('logs', exist_ok=True)
    fh = logging.FileHandler('logs/tasks.log')
    fh.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(fh)

# Redis utility to push result
class RedisClient:
    def __init__(self, redis_url):
        self.redis = redis.Redis.from_url(redis_url, decode_responses=True)
        self.redis.ping()

    def insert_into_output_queue(self, data):
        self.redis.rpush(OUTPUT_QUEUE, json.dumps(data))

    def get_queue_length(self, queue_name):
        return self.redis.llen(queue_name)

# Health check
class HealthCheckHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        if self.path == '/health':
            try:
                client = RedisClient(REDIS_URL)
                data = {
                    "status": "healthy",
                    "output_queue_length": client.get_queue_length(OUTPUT_QUEUE),
                    "timestamp": time.time()
                }
            except Exception as e:
                data = {
                    "status": "unhealthy",
                    "error": str(e),
                    "timestamp": time.time()
                }

            try:
                self.send_response(HTTPStatus.OK)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                self.wfile.write(json.dumps(data).encode())
            except (BrokenPipeError, ConnectionResetError, socket.error) as e:
                logger.warning(f"[HEALTH] Client disconnected before response could be sent: {e}")

    def log_message(self, format, *args):
        return

def run_health_server(port=8030):
    server = ThreadingHTTPServer(('', port), HealthCheckHandler)
    logger.info(f"[HEALTH] Server running on port {port}")
    server.serve_forever()

@app.task(bind=True, max_retries=3, name='process_retro_task')
def process_retro_task(self, payload):
    start_time = time.time()
    redis_client = RedisClient(REDIS_URL)
    db_ops = DbOps()
    required_fields = {"request_id", "target_smiles"}
    if not all(field in payload for field in required_fields):
        missing = required_fields - payload.keys()
        error_msg = f"Missing required fields: {missing}"
        logger.error(error_msg)
        if "request_id" in payload:
            redis_client.insert_into_output_queue({
                "request_id": payload.get("request_id"),
                "molecule_name": payload.get("molecule_name", "unknown"),
                "status": "FAILED",
                "error_message": error_msg,
                "task_id": self.request.id,
                "processing_time": time.time() - start_time
            })
        return False

    request_id = str(payload["request_id"])
    REQUEST_ID.set(request_id)  # Set the request_id in context variable
    molecule_name = payload["molecule_name"]
    target_smiles = payload["target_smiles"]

    for env_key, payload_key in {
        'MAX_DEPTH': 'max_depth',
        'SYNTHESIS_SCORE_THRESHOLD': 'synthesis_score_threshold',
        'MIN_FORWARD_PROB': 'min_forward_prob',
        'MIN_CERTAINITY_SCORE': 'min_certainity_score',
        'HEAVY_METAL_THRESHOLD': 'heavy_metal_threshold',
        'BEAM_WIDTH': 'beam_width',
        'PRUNING_FACTOR': 'pruning_factor',
        'BEAM_BASED_PRUNING': 'beam_based_pruning',
        'MAX_ROUTES': 'max_routes'
    }.items():
        if payload_key in payload:
            os.environ[env_key] = str(payload[payload_key])

    try:
        db_ops.insert_log(
            request_id=request_id,
            input_type="SMILES",
            input_value=target_smiles,
            status=RetroStatus.RUNNING,
        )
        logger.info(f"[START] Processing task {request_id} with SMILES: {target_smiles}")
        total_routes = retro_runner(target_smiles, request_id=request_id)
        processing_time = time.time() - start_time
        logger.info(f"[SUCCESS] Completed task {request_id} in {processing_time:.2f}s")
        db_ops.insert_log(
            request_id=request_id,
            input_type="SMILES",
            input_value=target_smiles,
            status=RetroStatus.COMPLETED,
        )
        result = app.send_task(
                'retro_result_handler',
                args=[{
                    "request_id": request_id,
                    "molecule_name": molecule_name,
                    "status": "SUCCESS",
                    "total_routes": total_routes,
                    "processed_at": time.time(),
                    "task_id": self.request.id,
                    "processing_time": processing_time
                }],
                queue=OUTPUT_QUEUE
            )
        return True
    except Exception as e:
        error_msg = str(e)
        logger.error(f"[ERROR] {request_id}: {error_msg}")
        logger.error(traceback.format_exc())
        if self.request.retries < self.max_retries:
            raise self.retry(countdown=60 * (2 ** self.request.retries))
        
        db_ops.insert_log(
            request_id=request_id,
            input_type="SMILES",
            input_value=target_smiles,
            status=RetroStatus.FAILED,
            user_id=payload.get("user_id", ""),
            tenant_id=payload.get("tenant_id", ""),
            error_message=error_msg
        )

        result = app.send_task(
                'retro_result_handler',
                args=[{
                    "request_id": request_id,
                    "molecule_name": molecule_name,
                    "status": "FAILED",
                    "error_message": error_msg,
                    "failed_at": time.time(),
                    "task_id": self.request.id,
                    "retries": self.request.retries,
                    "processing_time": time.time() - start_time
                }],
                queue=OUTPUT_QUEUE
            )
        return False

def main():
    
    Thread(target=run_health_server, daemon=True).start()
    logger.info("[MAIN] Celery worker will pull directly from Redis broker.")
    app.worker_main([
        'worker',
        '--loglevel=INFO',
        '--concurrency=1',
        '--pool=prefork',
        '--without-gossip',
        '--without-mingle',
        '--without-heartbeat'
    ])

if __name__ == "__main__":
    main()
