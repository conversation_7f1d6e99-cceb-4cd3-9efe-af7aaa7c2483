
FROM python:3.8.16

WORKDIR /app

# Environment variables

# Copy and install dependencies
COPY . .

RUN pip install --no-cache-dir -r requirements.txt

# Copy the zipped assets and the rest of the app


# Unzip assets.zip into the /app/assets directory
RUN apt-get update && apt-get install -y unzip && \
    mkdir -p temp && \
    unzip assets.zip -d temp && \
    mkdir -p assets && \
    mv temp/assets/* ./assets/ && \
    rm -rf temp assets.zip

EXPOSE 8080

CMD ["python3", "main.py"]
