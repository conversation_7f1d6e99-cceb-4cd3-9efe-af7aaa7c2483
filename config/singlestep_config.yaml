# Project configuration
project_name: ''
Resume_predictions_from_path: ''

# General settings:
min_solved_routes: 200          # Number of solved routes to reach to stop teh search
max_iteration: 2                # Max number of iteration search (recommended min 20)
max_mol_per_iteration: 15      # max number of molecules to extend per iteration
Commercial_exclusions: ['']     # List of molecular SMILES to exclude from the commercial set
step_penalties_rate: 0.8        # Score penalty applied for each step
tmp_file_path: 'output/'           # Folder in which storing onmt prediction files
log: True                       # Enables logs
commercial_file_path: 'assets/stocks/dw_Commercial_canonical.smi' # 'stocks/dw_Commercial_canonical.smi' # List of target commercial compounds

# Tagging settings
mark_count: 6                   # Max number of atoms to tag
neighbors: True                 # Tag neighboring atoms only or not
Random_Tagging: True            # Enables exhaustive/random tagging
AutoTagging: True               # Enables Transformer-based tagging
AutoTagging_Beam_Size: 100       # Number of tags to generate by the tagging Transformer
Substructure_Tagging: True      # Enable tagging by matching substructure
list_substructures_path: 'assets/stocks/list_conditionnal_substructures_tags_R2.csv'
list_substr_path_ENZR: ''

# USPTO Retrosynthesis parameters:
Retro_USPTO: True               # Enables USPTO T1 model
Fwd_USPTO_Reag_Pred: True       # Enables USPTO T3 model

# Beam sizes:
Retro_beam_size: 20              # Number of set of precursors to predict per molecule
USPTO_Reag_Beam_Size: 6         # Beam size (number of) reactant sets to predict (T2) and try on T3

# Tree search performance settings:
mark_locations_filter: 1        # percentage of tags to keep, randomly selected, use for large molecules
tree_only_best: 0.75            # percentage of overall branches to keep after an iteration
tree_min_best: 5000             # min number of branches to keep after an iteration
tree_max_best: 50000            # max number of branches to keep after an iteration
branching_max_expansion: 15     # max number of branching per molecule (node, best score first)

# Transformer model paths:
Model_Folder:       'assets/mttl_models'   # Folder path containing Transformer models

# Transformer model paths, USPTO:
USPTO_AutoTag_path: 'USPTO_STEREO_separated_T0_AutoTag_260000.pt'
USPTO_T1_path:      'USPTO_STEREO_separated_T1_Retro_255000.pt'
USPTO_T2_path:      'USPTO_STEREO_separated_T2_Reagent_Pred_225000.pt'
USPTO_T3_path:      'USPTO_STEREO_separated_T3_Forward_255000.pt'

# Transformer model paths, ENZR:
ENZR_AutoTag_path:  'AutoTag_ENZR_USPTO.pt'
ENZR_T1_path:       'T1_ENZR_USPTO.pt'
ENZR_T2_path:       'T2_ENZR_USPTO.pt'
ENZR_T3_path:       'T3_ENZR_USPTO.pt'

# Prediction filters (do not use):
similarity_filter: False        # Filters out retrosynthesis predictions too similar to the target 
confidence_filter: False        # Filters out low T3 confident predictions
